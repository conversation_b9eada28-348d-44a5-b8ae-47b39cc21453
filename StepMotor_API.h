#ifndef __STEPMOTOR_API_H
#define __STEPMOTOR_API_H

#include "sys.h"

/***********************************************
公司：轮趣科技（深圳）有限公司
品牌：WHEELTEC
网址：wheeltec.net
淘宝店铺：shop114407458.taobao.com 
速卖通: https://minibalance.aliexpress.com/store/4455017
版本：V1.0
修改时间：2024-11-14

步进电机API - 简化版步进电机控制接口
基于ATD5984驱动器的高级API封装
支持双路步进电机独立控制

Brand: WHEELTEC
Website: wheeltec.net
Taobao shop: shop114407458.taobao.com 
Aliexpress: https://minibalance.aliexpress.com/store/4455017
Version: V1.0
Update：2024-11-14
All rights reserved
***********************************************/

//=============================================================================
// 宏定义
//=============================================================================

#define MOTOR_1                 1       // 电机1 ID
#define MOTOR_2                 2       // 电机2 ID

#define MOTOR_STEP_ANGLE        1.8f    // 步进电机步距角(度) - 可根据实际电机修改
#define MOTOR_STEPS_PER_REV     200     // 每圈步数 (360/1.8=200) - 可根据实际电机修改

#define SPEED_LEVEL_MIN         1       // 最小速度等级
#define SPEED_LEVEL_MAX         10      // 最大速度等级

#define MOTOR_MAX_ANGLE         3600.0f // 最大角度限制(度) - 可修改，0表示无限制

//=============================================================================
// 枚举定义
//=============================================================================

// 电机旋转方向枚举
typedef enum
{
    MOTOR_CW  = 0,      // 顺时针旋转
    MOTOR_CCW = 1       // 逆时针旋转
} MotorDirection_t;

// 电机运行状态枚举
typedef enum
{
    MOTOR_STOPPED = 0,  // 电机停止
    MOTOR_RUNNING = 1   // 电机运行中
} MotorStatus_t;

//=============================================================================
// 结构体定义
//=============================================================================

// 速度配置结构体 - 定义不同速度等级对应的PWM参数
typedef struct
{
    u16 arr_value;          // 自动重装载值 - 值越小频率越高，速度越快
    u16 psc_value;          // 预分频值 - 配合arr_value调节PWM频率
    char description[20];   // 速度描述 - 中文说明该速度等级的特点
} SpeedConfig_t;

// 电机状态结构体 - 记录单个电机的所有状态信息
typedef struct
{
    u8 motor_id;                // 电机ID (1或2)
    u32 current_steps;          // 当前累计步数 - 用于位置跟踪
    float current_angle;        // 当前角度(度) - 实时计算的角度位置
    u8 speed_level;             // 当前速度等级 (1-10) - 用户设置的速度
    MotorDirection_t direction; // 当前旋转方向 - 最后一次设置的方向
    MotorStatus_t status;       // 运行状态 - 当前是否在运行
    u32 target_steps;           // 目标步数 - 用于精确控制
    u8 is_initialized;          // 初始化标志 - 标记电机是否已初始化
} StepMotor_t;

//=============================================================================
// API函数声明
//=============================================================================

/**
 * @brief  步进电机系统初始化
 * @note   一键初始化函数，用户无需手动调用底层初始化函数
 *         自动完成硬件初始化、PWM配置、GPIO设置等
 * @param  无
 * @retval 0: 初始化成功  1: 初始化失败
 * @usage  在main函数开始时调用一次即可
 */
u8 StepMotor_Init(void);

/**
 * @brief  设置电机速度等级
 * @note   速度等级1-10，数字越大速度越快
 *         等级1: 最慢速度，适合精确定位
 *         等级5: 中等速度，适合一般应用
 *         等级10: 最快速度，适合快速移动
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @param  speed_level: 速度等级 (1-10)
 * @retval 0: 设置成功  1: 参数错误
 * @usage  StepMotor_SetSpeed(MOTOR_1, 5); // 设置电机1为中等速度
 */
u8 StepMotor_SetSpeed(u8 motor_id, u8 speed_level);

/**
 * @brief  控制电机旋转指定步数
 * @note   精确的步数控制，适合需要准确位置控制的场合
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @param  steps: 旋转步数 (1-65535)
 * @param  direction: 旋转方向 (MOTOR_CW 或 MOTOR_CCW)
 * @retval 0: 执行成功  1: 参数错误
 * @usage  StepMotor_RotateSteps(MOTOR_1, 100, MOTOR_CW); // 电机1顺时针转100步
 */
u8 StepMotor_RotateSteps(u8 motor_id, u32 steps, MotorDirection_t direction);

/**
 * @brief  控制电机旋转指定角度
 * @note   角度控制，自动转换为对应步数
 *         角度精度取决于电机步距角(默认1.8度)
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @param  angle: 旋转角度(度) - 正数为顺时针，负数为逆时针
 * @retval 0: 执行成功  1: 参数错误
 * @usage  StepMotor_RotateAngle(MOTOR_1, 90.0); // 电机1顺时针转90度
 *         StepMotor_RotateAngle(MOTOR_1, -45.0); // 电机1逆时针转45度
 */
u8 StepMotor_RotateAngle(u8 motor_id, float angle);

/**
 * @brief  停止指定电机
 * @note   立即停止电机运行，保持当前位置
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @retval 0: 停止成功  1: 参数错误
 * @usage  StepMotor_Stop(MOTOR_1); // 停止电机1
 */
u8 StepMotor_Stop(u8 motor_id);

/**
 * @brief  获取电机当前角度
 * @note   返回电机从初始化或上次归零后的累计角度
 *         角度范围：0-359.9度，超过360度自动取模
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @retval 当前角度值(度) - 返回-1表示参数错误
 * @usage  float angle = StepMotor_GetAngle(MOTOR_1); // 获取电机1当前角度
 */
float StepMotor_GetAngle(u8 motor_id);

/**
 * @brief  电机角度归零
 * @note   将指定电机的角度和步数计数器清零
 *         不会移动电机，只是重置位置记录
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @retval 0: 归零成功  1: 参数错误
 * @usage  StepMotor_Reset(MOTOR_1); // 将电机1位置归零
 */
u8 StepMotor_Reset(u8 motor_id);

/**
 * @brief  获取电机状态信息
 * @note   获取电机的详细状态信息，用于调试和监控
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @retval 指向电机状态结构体的指针 - 返回NULL表示参数错误
 * @usage  StepMotor_t* status = StepMotor_GetStatus(MOTOR_1);
 *         if(status != NULL) printf("角度: %.1f度\n", status->current_angle);
 */
StepMotor_t* StepMotor_GetStatus(u8 motor_id);

/**
 * @brief  设置电机目标角度(绝对位置控制)
 * @note   电机将自动旋转到指定的绝对角度位置
 *         会自动选择最短路径(顺时针或逆时针)
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @param  target_angle: 目标角度(度) 0-359.9
 * @retval 0: 设置成功  1: 参数错误
 * @usage  StepMotor_SetTargetAngle(MOTOR_1, 180.0); // 电机1转到180度位置
 */
u8 StepMotor_SetTargetAngle(u8 motor_id, float target_angle);

//=============================================================================
// 系统内部函数声明 (用于中断处理)
//=============================================================================

/**
 * @brief  步数跟踪更新函数
 * @note   此函数应在TIM2中断处理函数中调用，用于实时更新电机状态
 *         不建议用户直接调用此函数
 * @param  无
 * @retval 无
 * @usage  在TIM2_IRQHandler中调用：StepMotor_UpdateStepCount();
 */
void StepMotor_UpdateStepCount(void);

//=============================================================================
// 高级功能函数声明 (可选使用)
//=============================================================================

/**
 * @brief  双电机同步旋转
 * @note   两个电机同时开始旋转，适合需要协调运动的场合
 * @param  angle1: 电机1旋转角度(度)
 * @param  angle2: 电机2旋转角度(度)
 * @retval 0: 执行成功  1: 参数错误
 * @usage  StepMotor_SyncRotate(90.0, -90.0); // 电机1顺时针90度，电机2逆时针90度
 */
u8 StepMotor_SyncRotate(float angle1, float angle2);

/**
 * @brief  停止所有电机
 * @note   同时停止两个电机
 * @param  无
 * @retval 无
 * @usage  StepMotor_StopAll(); // 停止所有电机
 */
void StepMotor_StopAll(void);

#endif /* __STEPMOTOR_API_H */
