#include "sys.h"
#include "StepMotor_API.h"
/***********************************************
????????????????????????
????WHEELTEC
??????wheeltec.net
????????shop114407458.taobao.com 
?????: https://minibalance.aliexpress.com/store/4455017
?汾??V1.0
??????2024-11-14

Brand: WHEELTEC
Website: wheeltec.net
Taobao shop: shop114407458.taobao.com 
Aliexpress: https://minibalance.aliexpress.com/store/4455017
Version: V1.0
Update??2024-11-14
All rights reserved
***********************************************/



int main(void)
{
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
	delay_init(72);                 

	uart_init(115200);							//串口初始化

	// 【新API使用】一键初始化步进电机系统
	if(StepMotor_Init() == 0)
	{
		printf("步进电机系统初始化成功！\r\n");
	}
	else
	{
		printf("步进电机系统初始化失败！\r\n");
		return -1;
	}

	TIM2_Init(199, 7199);				//10ms来一次中断处理程序，用于位置跟踪

	printf("=== 步进电机角度控制修复验证测试 ===\r\n");

	// 测试1：小角度旋转
	printf("\r\n测试1：电机1旋转30度\r\n");
	printf("旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
	StepMotor_RotateAngle(MOTOR_1, 30.0f);
	printf("旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
	printf("电机状态: %s\r\n",
		   (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");

	delay_ms(1000);

	// 测试2：90度旋转
	printf("\r\n测试2：电机1旋转90度\r\n");
	printf("旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
	StepMotor_RotateAngle(MOTOR_1, 90.0f);
	printf("旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
	printf("电机状态: %s\r\n",
		   (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");

	delay_ms(1000);

	// 测试3：反向旋转
	printf("\r\n测试3：电机1反向旋转45度\r\n");
	printf("旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
	StepMotor_RotateAngle(MOTOR_1, -45.0f);
	printf("旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
	printf("电机状态: %s\r\n",
		   (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");

	if(StepMotor_GetStatus(MOTOR_1)->status == MOTOR_STOPPED)
	{
		printf("\r\n修复成功：电机能够精确控制角度并自动停止！\r\n");
	}
	else
	{
		printf("\r\n问题仍存在：电机仍在运行\r\n");
	}

	while(1)
	{
		delay_ms(100);  // 主循环延时
	}
}
