/***********************************************
步进电机API修复测试示例
演示修复后的API如何正确控制电机转到指定角度后自动停止

修复内容：
1. 电机现在会在转到指定步数/角度后自动停止
2. 使用精确的时间计算来控制运行时间
3. 提供实时的角度反馈

版本：V1.1
修改时间：2024-11-14
***********************************************/

#include "sys.h"
#include "StepMotor_API.h"

//=============================================================================
// 修复验证示例函数
//=============================================================================

/**
 * @brief  基本控制修复验证
 * @note   验证电机能够正确停止在指定位置
 */
void Example_FixedBasicControl(void)
{
    printf("\r\n=== 修复验证：基本控制测试 ===\r\n");
    
    // 1. 步数控制测试（应该自动停止）
    printf("测试1：电机1顺时针旋转100步\r\n");
    printf("旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    
    StepMotor_RotateSteps(MOTOR_1, 100, MOTOR_CW);
    // 函数返回时电机应该已经停止
    
    printf("旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    printf("电机状态: %s\r\n", 
           (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");
    
    delay_ms(1000);
    
    // 2. 角度控制测试
    printf("\r\n测试2：电机1旋转90度\r\n");
    printf("旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    
    StepMotor_RotateAngle(MOTOR_1, 90.0f);
    // 函数返回时电机应该已经停止
    
    printf("旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    printf("电机状态: %s\r\n", 
           (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");
    
    delay_ms(1000);
    
    // 3. 反向旋转测试
    printf("\r\n测试3：电机1逆时针旋转45度\r\n");
    printf("旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    
    StepMotor_RotateAngle(MOTOR_1, -45.0f);
    
    printf("旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    printf("电机状态: %s\r\n", 
           (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");
    
    printf("=== 基本控制修复验证完成 ===\r\n");
}

/**
 * @brief  精确定位验证
 * @note   验证电机能够精确定位到指定角度
 */
void Example_PrecisionPositioning(void)
{
    float target_angles[] = {0, 45, 90, 135, 180, 225, 270, 315};
    u8 i;
    
    printf("\r\n=== 修复验证：精确定位测试 ===\r\n");
    
    // 先归零
    StepMotor_Reset(MOTOR_1);
    printf("电机1已归零，当前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    
    for(i = 0; i < 8; i++)
    {
        printf("\r\n定位测试 %d/8：目标角度 %.0f度\r\n", i+1, target_angles[i]);
        printf("定位前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
        
        // 使用绝对定位
        StepMotor_SetTargetAngle(MOTOR_1, target_angles[i]);
        
        printf("定位后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
        printf("误差: %.1f度\r\n", 
               target_angles[i] - StepMotor_GetAngle(MOTOR_1));
        printf("电机状态: %s\r\n", 
               (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");
        
        delay_ms(1500);  // 观察间隔
    }
    
    printf("=== 精确定位验证完成 ===\r\n");
}

/**
 * @brief  速度控制验证
 * @note   验证不同速度下电机都能正确停止
 */
void Example_SpeedControlTest(void)
{
    u8 speed_levels[] = {1, 3, 5, 7, 10};
    u8 i;
    
    printf("\r\n=== 修复验证：速度控制测试 ===\r\n");
    
    for(i = 0; i < 5; i++)
    {
        printf("\r\n速度测试 %d/5：速度等级 %d\r\n", i+1, speed_levels[i]);
        
        StepMotor_SetSpeed(MOTOR_1, speed_levels[i]);
        printf("设置速度等级: %d\r\n", speed_levels[i]);
        printf("旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
        
        // 旋转90度
        StepMotor_RotateAngle(MOTOR_1, 90.0f);
        
        printf("旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
        printf("电机状态: %s\r\n", 
               (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");
        
        delay_ms(1000);
    }
    
    printf("=== 速度控制验证完成 ===\r\n");
}

/**
 * @brief  双电机同步验证
 * @note   验证双电机能够同步运行并正确停止
 */
void Example_DualMotorSync(void)
{
    printf("\r\n=== 修复验证：双电机同步测试 ===\r\n");
    
    // 设置相同速度
    StepMotor_SetSpeed(MOTOR_1, 5);
    StepMotor_SetSpeed(MOTOR_2, 5);
    
    printf("双电机同步旋转测试\r\n");
    printf("电机1旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    printf("电机2旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_2));
    
    // 同步旋转：电机1顺时针90度，电机2逆时针90度
    StepMotor_SyncRotate(90.0f, -90.0f);
    
    printf("电机1旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    printf("电机2旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_2));
    printf("电机1状态: %s\r\n", 
           (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");
    printf("电机2状态: %s\r\n", 
           (StepMotor_GetStatus(MOTOR_2)->status == MOTOR_RUNNING) ? "运行中" : "已停止");
    
    printf("=== 双电机同步验证完成 ===\r\n");
}

/**
 * @brief  修复验证主函数
 * @note   运行所有修复验证测试
 */
void StepMotor_FixVerificationMain(void)
{
    // 系统基础初始化
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    delay_init(72);
    uart_init(115200);
    
    printf("\r\n");
    printf("========================================\r\n");
    printf("  步进电机API修复验证测试开始\r\n");
    printf("  修复问题：电机现在会自动停止\r\n");
    printf("========================================\r\n");
    
    // 初始化步进电机系统
    if(StepMotor_Init() == 0)
    {
        printf("步进电机系统初始化成功！\r\n");
    }
    else
    {
        printf("步进电机系统初始化失败！\r\n");
        return;
    }
    
    // 启动定时器用于位置跟踪
    TIM2_Init(199, 7199);
    
    // 运行修复验证测试
    Example_FixedBasicControl();      // 基本控制验证
    delay_ms(2000);
    
    Example_PrecisionPositioning();   // 精确定位验证
    delay_ms(2000);
    
    Example_SpeedControlTest();       // 速度控制验证
    delay_ms(2000);
    
    Example_DualMotorSync();          // 双电机同步验证
    
    printf("\r\n");
    printf("========================================\r\n");
    printf("  所有修复验证测试完成！\r\n");
    printf("  电机现在能够正确停止在指定位置\r\n");
    printf("========================================\r\n");
}

/**
 * @brief  简单测试函数
 * @note   快速验证修复效果
 */
void QuickTest(void)
{
    printf("\r\n=== 快速测试：电机自动停止验证 ===\r\n");
    
    StepMotor_Init();
    TIM2_Init(199, 7199);
    
    printf("测试：电机1旋转90度后应自动停止\r\n");
    printf("旋转前角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    
    StepMotor_RotateAngle(MOTOR_1, 90.0f);
    
    printf("旋转后角度: %.1f度\r\n", StepMotor_GetAngle(MOTOR_1));
    printf("电机状态: %s\r\n", 
           (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? "运行中" : "已停止");
    
    if(StepMotor_GetStatus(MOTOR_1)->status == MOTOR_STOPPED)
    {
        printf("✓ 修复成功：电机已自动停止！\r\n");
    }
    else
    {
        printf("✗ 问题仍存在：电机仍在运行\r\n");
    }
}
