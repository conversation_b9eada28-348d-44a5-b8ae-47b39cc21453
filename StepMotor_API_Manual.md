# 步进电机API使用手册

## 版本信息
- **版本**: V1.1 (重要修复版本)
- **更新时间**: 2024-11-14
- **适用硬件**: STM32F103RCT6 + D36A步进电机驱动器
- **公司**: 轮趣科技（深圳）有限公司
- **品牌**: WHEELTEC

## 🔧 V1.1 重要修复说明

### 修复的问题
**原问题**: 电机调用`StepMotor_RotateAngle()`或`StepMotor_RotateSteps()`后会一直转动，不会自动停止在指定角度。

### 修复内容
1. **重新设计控制逻辑**: 使用精确的时间计算来控制电机运行时间
2. **自动停止机制**: 电机转到指定步数后自动停止PWM输出
3. **精确位置控制**: 函数调用完成时，电机已经停止在目标位置
4. **实时状态更新**: 提供准确的位置反馈和运行状态

### 修复效果对比
```c
// V1.0 (有问题的版本)
StepMotor_RotateAngle(MOTOR_1, 90.0f);  // 电机会一直转动
// 需要手动调用 StepMotor_Stop(MOTOR_1) 来停止

// V1.1 (修复后的版本)
StepMotor_RotateAngle(MOTOR_1, 90.0f);  // 电机自动停止在90度位置
float angle = StepMotor_GetAngle(MOTOR_1);  // 获取当前角度，约为90度
```

---

## 目录
1. [API概述](#api概述)
2. [快速入门](#快速入门)
3. [硬件连接](#硬件连接)
4. [API函数详解](#api函数详解)
5. [修复验证示例](#修复验证示例)
6. [常见问题](#常见问题)
7. [故障排除](#故障排除)

---

## API概述

### 主要特性
- **一键初始化**: 无需手动配置复杂的PWM参数
- **精确控制**: 电机转到指定角度后自动停止 ⭐ **V1.1新增**
- **简化接口**: 提供直观的角度和步数控制函数
- **实时跟踪**: 自动跟踪电机位置和角度
- **双电机支持**: 独立控制两个步进电机
- **速度分级**: 10个预设速度等级，满足不同应用需求
- **中文注释**: 详细的中文注释，便于理解和维护

### 架构设计
```
用户应用层 (简单调用，自动停止)
    ↓
StepMotor_API (高级API层，精确控制)
    ↓
ATD5984驱动层 (硬件抽象层)
    ↓
STM32硬件层 (PWM + GPIO控制)
```

---

## 快速入门

### 第一步：包含头文件
```c
#include "StepMotor_API.h"
```

### 第二步：系统初始化
```c
int main(void)
{
    // 基础系统初始化
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    delay_init(72);
    uart_init(115200);
    
    // 步进电机一键初始化
    if(StepMotor_Init() == 0)
    {
        printf("步进电机初始化成功！\n");
    }
    
    // 启动定时器用于位置跟踪
    TIM2_Init(199, 7199);  // 10ms中断
    
    // 您的应用代码...
}
```

### 第三步：基本使用 (V1.1修复后)
```c
// 设置电机速度
StepMotor_SetSpeed(MOTOR_1, 5);  // 中等速度

// 角度控制 - 现在会自动停止！
StepMotor_RotateAngle(MOTOR_1, 90.0f);   // 顺时针90度，自动停止
printf("当前角度: %.1f度\n", StepMotor_GetAngle(MOTOR_1));  // 约90度

StepMotor_RotateAngle(MOTOR_1, -45.0f);  // 逆时针45度，自动停止
printf("当前角度: %.1f度\n", StepMotor_GetAngle(MOTOR_1));  // 约45度

// 步数控制 - 现在会自动停止！
StepMotor_RotateSteps(MOTOR_1, 100, MOTOR_CW);  // 顺时针100步，自动停止

// 检查电机状态
StepMotor_t* status = StepMotor_GetStatus(MOTOR_1);
printf("电机状态: %s\n", 
       (status->status == MOTOR_RUNNING) ? "运行中" : "已停止");
```

---

## 硬件连接

### STM32F103RCT6引脚分配
| 功能 | 引脚 | 说明 |
|------|------|------|
| 电机1 PWM | PC8 | TIM8_CH3，步进脉冲信号 |
| 电机2 PWM | PC9 | TIM8_CH4，步进脉冲信号 |
| 电机1 DIR | PC13 | 方向控制信号 |
| 电机2 DIR | PC14 | 方向控制信号 |
| 电机1 SLEEP | PD2 | 使能控制信号 |
| 电机2 SLEEP | PC12 | 使能控制信号 |

---

## API函数详解

### 1. 系统初始化函数

#### StepMotor_Init()
```c
u8 StepMotor_Init(void);
```
**功能**: 一键初始化步进电机系统

**返回值**: 
- `0`: 初始化成功
- `1`: 初始化失败

**使用示例**:
```c
if(StepMotor_Init() == 0)
{
    printf("初始化成功\n");
}
```

### 2. 运动控制函数 (V1.1修复)

#### StepMotor_RotateSteps() ⭐ **已修复**
```c
u8 StepMotor_RotateSteps(u8 motor_id, u32 steps, MotorDirection_t direction);
```
**功能**: 控制电机旋转指定步数，**自动停止**

**修复内容**: 
- 使用精确时间计算控制运行时间
- 函数返回时电机已停止在目标位置
- 自动更新位置信息

**使用示例**:
```c
// 电机1顺时针旋转100步，自动停止
StepMotor_RotateSteps(MOTOR_1, 100, MOTOR_CW);
printf("当前角度: %.1f度\n", StepMotor_GetAngle(MOTOR_1));  // 显示实际角度
```

#### StepMotor_RotateAngle() ⭐ **已修复**
```c
u8 StepMotor_RotateAngle(u8 motor_id, float angle);
```
**功能**: 控制电机旋转指定角度，**自动停止**

**修复内容**: 
- 精确角度控制，转到指定角度后自动停止
- 支持正负角度（顺时针/逆时针）
- 实时位置更新

**使用示例**:
```c
StepMotor_RotateAngle(MOTOR_1, 90.0f);   // 顺时针90度，自动停止
StepMotor_RotateAngle(MOTOR_1, -45.0f);  // 逆时针45度，自动停止

// 检查结果
float angle = StepMotor_GetAngle(MOTOR_1);
printf("当前角度: %.1f度\n", angle);
```

### 3. 状态查询函数

#### StepMotor_GetAngle()
```c
float StepMotor_GetAngle(u8 motor_id);
```
**功能**: 获取电机当前角度

**返回值**: 当前角度值(度) 0-359.9，或 -1(参数错误)

#### StepMotor_GetStatus()
```c
StepMotor_t* StepMotor_GetStatus(u8 motor_id);
```
**功能**: 获取电机详细状态信息

**返回值**: 指向电机状态结构体的指针，或 NULL(参数错误)

---

## 修复验证示例

### 基本验证测试
```c
void VerifyFix(void)
{
    StepMotor_Init();
    TIM2_Init(199, 7199);
    
    printf("修复验证：电机旋转90度后应自动停止\n");
    printf("旋转前角度: %.1f度\n", StepMotor_GetAngle(MOTOR_1));
    
    // 旋转90度 - 应该自动停止
    StepMotor_RotateAngle(MOTOR_1, 90.0f);
    
    printf("旋转后角度: %.1f度\n", StepMotor_GetAngle(MOTOR_1));
    printf("电机状态: %s\n", 
           (StepMotor_GetStatus(MOTOR_1)->status == MOTOR_RUNNING) ? 
           "运行中" : "已停止");
    
    // 验证结果
    if(StepMotor_GetStatus(MOTOR_1)->status == MOTOR_STOPPED)
    {
        printf("✓ 修复成功：电机已自动停止！\n");
    }
    else
    {
        printf("✗ 问题仍存在：电机仍在运行\n");
    }
}
```

### 精确定位验证
```c
void PrecisionTest(void)
{
    float target_angles[] = {0, 45, 90, 135, 180};
    u8 i;
    
    StepMotor_Reset(MOTOR_1);  // 归零
    
    for(i = 0; i < 5; i++)
    {
        printf("定位到%.0f度\n", target_angles[i]);
        StepMotor_SetTargetAngle(MOTOR_1, target_angles[i]);
        
        float actual = StepMotor_GetAngle(MOTOR_1);
        printf("实际角度: %.1f度，误差: %.1f度\n", 
               actual, target_angles[i] - actual);
    }
}
```

---

## 常见问题

### Q1: 电机不转动怎么办？
**A1**: 检查以下几点：
1. 确认已调用`StepMotor_Init()`
2. 确认已调用`TIM2_Init()`
3. 检查硬件连接
4. 检查电源供电

### Q2: 角度不准确怎么办？
**A2**: 
1. 确认步距角设置正确
2. 使用较低的速度等级
3. 检查机械传动间隙

### Q3: 如何验证修复效果？
**A3**: 
```c
// 简单验证
StepMotor_RotateAngle(MOTOR_1, 90.0f);
if(StepMotor_GetStatus(MOTOR_1)->status == MOTOR_STOPPED)
{
    printf("修复成功：电机已自动停止\n");
}
```

---

## 故障排除

### 故障现象1：电机仍然一直转动
**可能原因**: 
- 使用了旧版本的代码
- 未正确编译新版本

**解决方案**:
1. 确认使用V1.1版本的代码
2. 重新编译整个项目
3. 检查`StepMotor_RotateSteps`函数是否包含时间计算逻辑

### 故障现象2：电机停止位置不准确
**可能原因**:
- 速度过快导致失步
- 负载过重

**解决方案**:
1. 降低速度等级
2. 检查机械负载
3. 使用更精确的时间计算

---

## 技术支持

如有问题，请联系：
- **网址**: wheeltec.net
- **淘宝店铺**: shop114407458.taobao.com
- **速卖通**: https://minibalance.aliexpress.com/store/4455017

---

*本文档版权归轮趣科技（深圳）有限公司所有*
