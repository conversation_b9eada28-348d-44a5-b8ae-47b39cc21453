# STM32F103RCT6步进电机驱动API

## 项目简介

本项目为STM32F103RCT6控制D36A驱动双路步进电机提供了一套简单易用的高级API接口。通过封装底层硬件操作，用户无需了解复杂的PWM配置和GPIO控制细节，即可轻松实现步进电机的精确控制。

## 主要特性

- ✅ **一键初始化**: 替代复杂的手动配置，一个函数完成所有初始化
- ✅ **简化接口**: 提供直观的角度和步数控制函数
- ✅ **精确控制**: 电机转到指定角度后自动停止，无需手动控制
- ✅ **实时跟踪**: 自动跟踪电机位置和角度变化
- ✅ **双电机支持**: 独立控制两个步进电机
- ✅ **速度分级**: 10个预设速度等级，满足不同应用需求
- ✅ **中文注释**: 详细的中文注释，便于理解和维护
- ✅ **完整示例**: 提供丰富的使用示例和应用场景

## 🔧 重要修复 (V1.1)

**问题**: 之前版本中电机会一直转动，不会自动停止在指定角度。

**解决方案**:
- 重新设计了控制逻辑，使用精确的时间计算
- 电机现在会在转到指定步数/角度后自动停止
- 函数调用完成时，电机已经停止在目标位置

**验证方法**:
```c
// 现在这样调用，电机会自动停止在90度位置
StepMotor_RotateAngle(MOTOR_1, 90.0f);
printf("当前角度: %.1f度\n", StepMotor_GetAngle(MOTOR_1)); // 应该显示约90度
```

## 硬件要求

- **主控**: STM32F103RCT6
- **驱动器**: D36A步进电机驱动器
- **电机**: 1.8°步距角步进电机（可配置）
- **电源**: 根据电机规格提供合适的电源

## 快速开始

### 1. 文件结构
```
├── StepMotor_API.h          # API头文件
├── StepMotor_API.c          # API实现文件
├── StepMotor_Example.c      # 使用示例
├── StepMotor_API_Manual.md  # 详细使用手册
└── README.md               # 项目说明
```

### 2. 基本使用

#### 原有方式 vs 新API方式
```c
// 原有方式（复杂）
ATD5984_Init();                    // 硬件初始化
STEP12_PWM_Init(7199, 6);         // PWM配置
// 还需要手动计算步数、控制方向等...

// 新API方式（简单）
StepMotor_Init();                  // 一键初始化
StepMotor_RotateAngle(MOTOR_1, 90.0f);  // 直接角度控制
```

#### 完整示例
```c
#include "StepMotor_API.h"

int main(void)
{
    // 系统基础初始化
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    delay_init(72);
    uart_init(115200);
    
    // 步进电机一键初始化
    if(StepMotor_Init() == 0)
    {
        printf("步进电机初始化成功！\n");
    }
    
    // 启动定时器用于位置跟踪
    TIM2_Init(199, 7199);
    
    // 设置电机速度
    StepMotor_SetSpeed(MOTOR_1, 5);  // 中等速度
    
    while(1)
    {
        // 旋转90度
        StepMotor_RotateAngle(MOTOR_1, 90.0f);
        delay_ms(1000);
        
        // 获取当前角度
        float angle = StepMotor_GetAngle(MOTOR_1);
        printf("当前角度: %.1f度\n", angle);
        
        delay_ms(1000);
    }
}
```

### 3. 主要API函数

| 函数 | 功能 | 示例 |
|------|------|------|
| `StepMotor_Init()` | 一键初始化 | `StepMotor_Init();` |
| `StepMotor_SetSpeed()` | 设置速度等级(1-10) | `StepMotor_SetSpeed(MOTOR_1, 5);` |
| `StepMotor_RotateAngle()` | 角度控制 | `StepMotor_RotateAngle(MOTOR_1, 90.0f);` |
| `StepMotor_RotateSteps()` | 步数控制 | `StepMotor_RotateSteps(MOTOR_1, 100, MOTOR_CW);` |
| `StepMotor_GetAngle()` | 获取当前角度 | `float angle = StepMotor_GetAngle(MOTOR_1);` |
| `StepMotor_Stop()` | 停止电机 | `StepMotor_Stop(MOTOR_1);` |

## 文档说明

- **[StepMotor_API_Manual.md](StepMotor_API_Manual.md)**: 完整的API使用手册，包含详细的函数说明、参数解释、使用示例和故障排除指南
- **[StepMotor_Example.c](StepMotor_Example.c)**: 丰富的使用示例，包含基本控制、速度调节、角度控制、位置跟踪、双电机同步等应用场景

## 硬件连接

### STM32F103RCT6引脚分配
| 功能 | 引脚 | 说明 |
|------|------|------|
| 电机1 PWM | PC8 | TIM8_CH3，步进脉冲信号 |
| 电机2 PWM | PC9 | TIM8_CH4，步进脉冲信号 |
| 电机1 DIR | PC13 | 方向控制信号 |
| 电机2 DIR | PC14 | 方向控制信号 |
| 电机1 SLEEP | PD2 | 使能控制信号 |
| 电机2 SLEEP | PC12 | 使能控制信号 |

## 版本历史

### V1.1 (2024-11-14) - 重要修复
- 🔧 **修复关键问题**: 电机现在会在转到指定角度后自动停止
- 🔧 **优化控制逻辑**: 使用精确的时间计算来控制运行时间
- 🔧 **改进用户体验**: 函数调用完成时电机已停止在目标位置
- ✅ 添加修复验证示例代码
- ✅ 更新文档说明修复内容

### V1.0 (2024-11-14)
- ✅ 完成基础API设计和实现
- ✅ 支持双电机独立控制
- ✅ 实现10级速度控制
- ✅ 添加角度检测和位置跟踪
- ✅ 提供完整的使用示例
- ✅ 编写详细的中文文档

## 技术支持

- **公司**: 轮趣科技（深圳）有限公司
- **品牌**: WHEELTEC
- **网址**: wheeltec.net
- **淘宝店铺**: shop114407458.taobao.com
- **速卖通**: https://minibalance.aliexpress.com/store/4455017

## 许可证

本项目版权归轮趣科技（深圳）有限公司所有。

---

**让步进电机控制变得简单！** 🚀
