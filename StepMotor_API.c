#include "StepMotor_API.h"
#include "stm32f10x_tim.h"
/***********************************************
公司：轮趣科技（深圳）有限公司
品牌：WHEELTEC
网址：wheeltec.net
淘宝店铺：shop114407458.taobao.com 
速卖通: https://minibalance.aliexpress.com/store/4455017
版本：V1.0
修改时间：2024-11-14

步进电机API实现文件
基于ATD5984驱动器的高级API封装实现

Brand: WHEELTEC
Website: wheeltec.net
Taobao shop: shop114407458.taobao.com 
Aliexpress: https://minibalance.aliexpress.com/store/4455017
Version: V1.0
Update：2024-11-14
All rights reserved
***********************************************/

//=============================================================================
// 全局变量定义
//=============================================================================

// 电机状态数组 - 存储两个电机的状态信息
static StepMotor_t g_motors[2];

// 速度配置表 - 定义10个速度等级对应的PWM参数
static const SpeedConfig_t g_speed_configs[SPEED_LEVEL_MAX] = {
    {14399, 49, "mode-1"},    // 等级1: 最慢速度，适合精确定位
    {11999, 39, "mode-2"},    // 等级2: 很慢速度，精密控制
    {9599,  29, "mode-3"},      // 等级3: 慢速度，稳定运行
    {7199,  19, "mode-4"},    // 等级4: 中慢速度，一般应用
    {5999,  14, "mode-5"},    // 等级5: 中等速度，常用速度
    {4799,  11, "mode-6"},    // 等级6: 中快速度，快速移动
    {3599,  8,  "mode-7"},      // 等级7: 快速度，高效运行
    {2399,  5,  "mode-8"},    // 等级8: 很快速度，快速响应
    {1199,  2,  "mode-9"},    // 等级9: 极快速度，最高效率
    {599,   1,  "mode-10"}     // 等级10: 最快速度，极限速度
};

// 系统初始化标志
static u8 g_system_initialized = 0;

//=============================================================================
// 内部辅助函数
//=============================================================================

/**
 * @brief  步数跟踪更新函数 - 在定时器中断中调用
 * @note   此函数用于实时跟踪电机步数和角度变化
 *         应在TIM2中断处理函数中定期调用
 * @param  无
 * @retval 无
 */
void StepMotor_UpdateStepCount(void)
{
    u8 i;

    if(!g_system_initialized)
    {
        return;
    }

    // 更新两个电机的步数和角度
    for(i = 0; i < 2; i++)
    {
        if(g_motors[i].status == MOTOR_RUNNING)
        {
            // 检查是否达到目标步数
            if(g_motors[i].current_steps >= g_motors[i].target_steps)
            {
                // 停止电机
                if(g_motors[i].motor_id == MOTOR_1)
                {
                    TIM_SetCompare3(TIM8, 0);  // 停止电机1的PWM输出
                }
                else
                {
                    TIM_SetCompare4(TIM8, 0);  // 停止电机2的PWM输出
                }

                g_motors[i].status = MOTOR_STOPPED;
            }

            // 更新角度（保持在0-360度范围内）
            g_motors[i].current_angle = (float)(g_motors[i].current_steps % MOTOR_STEPS_PER_REV) * MOTOR_STEP_ANGLE;
        }
    }
}

/**
 * @brief  参数有效性检查
 * @param  motor_id: 电机ID
 * @retval 0: 参数有效  1: 参数无效
 */
static u8 _CheckMotorID(u8 motor_id)
{
    if(motor_id != MOTOR_1 && motor_id != MOTOR_2)
    {
        return 1; // 无效的电机ID
    }
    return 0;
}

/**
 * @brief  获取电机数组索引
 * @param  motor_id: 电机ID (MOTOR_1 或 MOTOR_2)
 * @retval 数组索引 (0 或 1)
 */
static u8 _GetMotorIndex(u8 motor_id)
{
    return (motor_id == MOTOR_1) ? 0 : 1;
}

//=============================================================================
// API函数实现
//=============================================================================

/**
 * @brief  步进电机系统初始化
 */
u8 StepMotor_Init(void)
{
    u8 i;
    
    // 调用底层硬件初始化
    ATD5984_Init();                    // 初始化ATD5984驱动器GPIO
    STEP12_PWM_Init(7199, 6);         // 初始化PWM，默认中等速度
    
    // 初始化电机状态结构体
    for(i = 0; i < 2; i++)
    {
        g_motors[i].motor_id = (i == 0) ? MOTOR_1 : MOTOR_2;
        g_motors[i].current_steps = 0;
        g_motors[i].current_angle = 0.0f;
        g_motors[i].speed_level = 5;           // 默认中等速度
        g_motors[i].direction = MOTOR_CW;      // 默认顺时针
        g_motors[i].status = MOTOR_STOPPED;    // 默认停止状态
        g_motors[i].target_steps = 0;
        g_motors[i].is_initialized = 1;        // 标记已初始化
    }
    
    g_system_initialized = 1;
    
    return 0; // 初始化成功
}

/**
 * @brief  设置电机速度等级
 */
u8 StepMotor_SetSpeed(u8 motor_id, u8 speed_level)
{
    u8 motor_index;
    
    // 参数检查
    if(_CheckMotorID(motor_id) || !g_system_initialized)
    {
        return 1; // 参数错误或系统未初始化
    }
    
    if(speed_level < SPEED_LEVEL_MIN || speed_level > SPEED_LEVEL_MAX)
    {
        return 1; // 速度等级超出范围
    }
    
    motor_index = _GetMotorIndex(motor_id);
    
    // 更新电机状态
    g_motors[motor_index].speed_level = speed_level;
    
    // 重新配置PWM参数
    // 注意：这里使用速度等级对应的配置
    STEP12_PWM_Init(g_speed_configs[speed_level - 1].arr_value, 
                    g_speed_configs[speed_level - 1].psc_value);
    
    return 0; // 设置成功
}

/**
 * @brief  停止指定电机
 */
u8 StepMotor_Stop(u8 motor_id)
{
    u8 motor_index;
    
    // 参数检查
    if(_CheckMotorID(motor_id) || !g_system_initialized)
    {
        return 1; // 参数错误或系统未初始化
    }
    
    motor_index = _GetMotorIndex(motor_id);
    
    // 停止PWM输出 - 通过设置占空比为0来停止
    if(motor_id == MOTOR_1)
    {
        TIM_SetCompare3(TIM8, 0);  // 停止电机1的PWM输出
    }
    else
    {
        TIM_SetCompare4(TIM8, 0);  // 停止电机2的PWM输出
    }
    
    // 更新电机状态
    g_motors[motor_index].status = MOTOR_STOPPED;
    
    return 0; // 停止成功
}

/**
 * @brief  获取电机状态信息
 */
StepMotor_t* StepMotor_GetStatus(u8 motor_id)
{
    u8 motor_index;
    
    // 参数检查
    if(_CheckMotorID(motor_id) || !g_system_initialized)
    {
        return NULL; // 参数错误或系统未初始化
    }
    
    motor_index = _GetMotorIndex(motor_id);
    
    return &g_motors[motor_index];
}

/**
 * @brief  电机角度归零
 */
u8 StepMotor_Reset(u8 motor_id)
{
    u8 motor_index;
    
    // 参数检查
    if(_CheckMotorID(motor_id) || !g_system_initialized)
    {
        return 1; // 参数错误或系统未初始化
    }
    
    motor_index = _GetMotorIndex(motor_id);
    
    // 重置位置信息
    g_motors[motor_index].current_steps = 0;
    g_motors[motor_index].current_angle = 0.0f;
    g_motors[motor_index].target_steps = 0;
    
    return 0; // 归零成功
}

/**
 * @brief  控制电机旋转指定步数
 */
u8 StepMotor_RotateSteps(u8 motor_id, u32 steps, MotorDirection_t direction)
{
    u8 motor_index;

    // 参数检查
    if(_CheckMotorID(motor_id) || !g_system_initialized)
    {
        return 1; // 参数错误或系统未初始化
    }

    if(steps == 0)
    {
        return 1; // 步数不能为0
    }

    motor_index = _GetMotorIndex(motor_id);

    // 设置旋转方向
    if(motor_id == MOTOR_1)
    {
        // 电机1方向控制 - PC13
        if(direction == MOTOR_CW)
        {
            GPIO_ResetBits(GPIOC, GPIO_Pin_13);  // 顺时针
        }
        else
        {
            GPIO_SetBits(GPIOC, GPIO_Pin_13);    // 逆时针
        }

        // 启动PWM输出
        TIM_SetCompare3(TIM8, TIM8->ARR / 2);  // 50%占空比
    }
    else
    {
        // 电机2方向控制 - PC14
        if(direction == MOTOR_CW)
        {
            GPIO_ResetBits(GPIOC, GPIO_Pin_14);  // 顺时针
        }
        else
        {
            GPIO_SetBits(GPIOC, GPIO_Pin_14);    // 逆时针
        }

        // 启动PWM输出
        TIM_SetCompare4(TIM8, TIM8->ARR / 2);  // 50%占空比
    }

    // 更新电机状态
    g_motors[motor_index].direction = direction;
    g_motors[motor_index].status = MOTOR_RUNNING;
    g_motors[motor_index].target_steps = g_motors[motor_index].current_steps + steps;

    // 更新步数和角度（这里简化处理，实际应在中断中更新）
    if(direction == MOTOR_CW)
    {
        g_motors[motor_index].current_steps += steps;
    }
    else
    {
        if(g_motors[motor_index].current_steps >= steps)
        {
            g_motors[motor_index].current_steps -= steps;
        }
        else
        {
            g_motors[motor_index].current_steps = 0;
        }
    }

    // 计算当前角度
    g_motors[motor_index].current_angle = (float)(g_motors[motor_index].current_steps % MOTOR_STEPS_PER_REV) * MOTOR_STEP_ANGLE;

    return 0; // 执行成功
}

/**
 * @brief  控制电机旋转指定角度
 */
u8 StepMotor_RotateAngle(u8 motor_id, float angle)
{
    u32 steps;
    MotorDirection_t direction;

    // 参数检查
    if(_CheckMotorID(motor_id) || !g_system_initialized)
    {
        return 1; // 参数错误或系统未初始化
    }

    // 角度转换为步数
    if(angle >= 0)
    {
        direction = MOTOR_CW;
        steps = (u32)(angle / MOTOR_STEP_ANGLE + 0.5f);  // 四舍五入
    }
    else
    {
        direction = MOTOR_CCW;
        steps = (u32)((-angle) / MOTOR_STEP_ANGLE + 0.5f);  // 四舍五入
    }

    // 调用步数控制函数
    return StepMotor_RotateSteps(motor_id, steps, direction);
}

/**
 * @brief  获取电机当前角度
 */
float StepMotor_GetAngle(u8 motor_id)
{
    u8 motor_index;

    // 参数检查
    if(_CheckMotorID(motor_id) || !g_system_initialized)
    {
        return -1.0f; // 参数错误或系统未初始化
    }

    motor_index = _GetMotorIndex(motor_id);

    return g_motors[motor_index].current_angle;
}

/**
 * @brief  设置电机目标角度(绝对位置控制)
 */
u8 StepMotor_SetTargetAngle(u8 motor_id, float target_angle)
{
    u8 motor_index;
    float current_angle, angle_diff;

    // 参数检查
    if(_CheckMotorID(motor_id) || !g_system_initialized)
    {
        return 1; // 参数错误或系统未初始化
    }

    if(target_angle < 0.0f || target_angle >= 360.0f)
    {
        return 1; // 角度超出范围
    }

    motor_index = _GetMotorIndex(motor_id);
    current_angle = g_motors[motor_index].current_angle;

    // 计算角度差值，选择最短路径
    angle_diff = target_angle - current_angle;

    if(angle_diff > 180.0f)
    {
        angle_diff -= 360.0f;  // 逆时针更短
    }
    else if(angle_diff < -180.0f)
    {
        angle_diff += 360.0f;  // 顺时针更短
    }

    // 执行旋转
    return StepMotor_RotateAngle(motor_id, angle_diff);
}

/**
 * @brief  双电机同步旋转
 */
u8 StepMotor_SyncRotate(float angle1, float angle2)
{
    u8 result1, result2;

    if(!g_system_initialized)
    {
        return 1; // 系统未初始化
    }

    // 同时启动两个电机
    result1 = StepMotor_RotateAngle(MOTOR_1, angle1);
    result2 = StepMotor_RotateAngle(MOTOR_2, angle2);

    // 如果任一电机启动失败，返回错误
    if(result1 != 0 || result2 != 0)
    {
        return 1;
    }

    return 0; // 执行成功
}

/**
 * @brief  停止所有电机
 */
void StepMotor_StopAll(void)
{
    if(g_system_initialized)
    {
        StepMotor_Stop(MOTOR_1);
        StepMotor_Stop(MOTOR_2);
    }
}
