Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to stepmotor_api.o(i.StepMotor_Init) for StepMotor_Init
    main.o(i.main) refers to stepmotor_api.o(i.StepMotor_RotateAngle) for StepMotor_RotateAngle
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for .data
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    stepmotor_api.o(i.StepMotor_GetAngle) refers to stepmotor_api.o(i._CheckMotorID) for _CheckMotorID
    stepmotor_api.o(i.StepMotor_GetAngle) refers to stepmotor_api.o(i._GetMotorIndex) for _GetMotorIndex
    stepmotor_api.o(i.StepMotor_GetAngle) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_GetAngle) refers to stepmotor_api.o(.bss) for .bss
    stepmotor_api.o(i.StepMotor_GetStatus) refers to stepmotor_api.o(i._CheckMotorID) for _CheckMotorID
    stepmotor_api.o(i.StepMotor_GetStatus) refers to stepmotor_api.o(i._GetMotorIndex) for _GetMotorIndex
    stepmotor_api.o(i.StepMotor_GetStatus) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_GetStatus) refers to stepmotor_api.o(.bss) for .bss
    stepmotor_api.o(i.StepMotor_Init) refers to atd5984.o(i.ATD5984_Init) for ATD5984_Init
    stepmotor_api.o(i.StepMotor_Init) refers to atd5984.o(i.STEP12_PWM_Init) for STEP12_PWM_Init
    stepmotor_api.o(i.StepMotor_Init) refers to stepmotor_api.o(.bss) for .bss
    stepmotor_api.o(i.StepMotor_Init) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_Reset) refers to stepmotor_api.o(i._CheckMotorID) for _CheckMotorID
    stepmotor_api.o(i.StepMotor_Reset) refers to stepmotor_api.o(i._GetMotorIndex) for _GetMotorIndex
    stepmotor_api.o(i.StepMotor_Reset) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_Reset) refers to stepmotor_api.o(.bss) for .bss
    stepmotor_api.o(i.StepMotor_RotateAngle) refers to stepmotor_api.o(i._CheckMotorID) for _CheckMotorID
    stepmotor_api.o(i.StepMotor_RotateAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    stepmotor_api.o(i.StepMotor_RotateAngle) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    stepmotor_api.o(i.StepMotor_RotateAngle) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    stepmotor_api.o(i.StepMotor_RotateAngle) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    stepmotor_api.o(i.StepMotor_RotateAngle) refers to stepmotor_api.o(i.StepMotor_RotateSteps) for StepMotor_RotateSteps
    stepmotor_api.o(i.StepMotor_RotateAngle) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to stepmotor_api.o(i._CheckMotorID) for _CheckMotorID
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to stepmotor_api.o(i._GetMotorIndex) for _GetMotorIndex
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_RotateSteps) refers to stepmotor_api.o(.bss) for .bss
    stepmotor_api.o(i.StepMotor_SetSpeed) refers to stepmotor_api.o(i._CheckMotorID) for _CheckMotorID
    stepmotor_api.o(i.StepMotor_SetSpeed) refers to stepmotor_api.o(i._GetMotorIndex) for _GetMotorIndex
    stepmotor_api.o(i.StepMotor_SetSpeed) refers to atd5984.o(i.STEP12_PWM_Init) for STEP12_PWM_Init
    stepmotor_api.o(i.StepMotor_SetSpeed) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_SetSpeed) refers to stepmotor_api.o(.bss) for .bss
    stepmotor_api.o(i.StepMotor_SetSpeed) refers to stepmotor_api.o(.constdata) for .constdata
    stepmotor_api.o(i.StepMotor_SetTargetAngle) refers to stepmotor_api.o(i._CheckMotorID) for _CheckMotorID
    stepmotor_api.o(i.StepMotor_SetTargetAngle) refers to stepmotor_api.o(i._GetMotorIndex) for _GetMotorIndex
    stepmotor_api.o(i.StepMotor_SetTargetAngle) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    stepmotor_api.o(i.StepMotor_SetTargetAngle) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    stepmotor_api.o(i.StepMotor_SetTargetAngle) refers to stepmotor_api.o(i.StepMotor_RotateAngle) for StepMotor_RotateAngle
    stepmotor_api.o(i.StepMotor_SetTargetAngle) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_SetTargetAngle) refers to stepmotor_api.o(.bss) for .bss
    stepmotor_api.o(i.StepMotor_Stop) refers to stepmotor_api.o(i._CheckMotorID) for _CheckMotorID
    stepmotor_api.o(i.StepMotor_Stop) refers to stepmotor_api.o(i._GetMotorIndex) for _GetMotorIndex
    stepmotor_api.o(i.StepMotor_Stop) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    stepmotor_api.o(i.StepMotor_Stop) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    stepmotor_api.o(i.StepMotor_Stop) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_Stop) refers to stepmotor_api.o(.bss) for .bss
    stepmotor_api.o(i.StepMotor_StopAll) refers to stepmotor_api.o(i.StepMotor_Stop) for StepMotor_Stop
    stepmotor_api.o(i.StepMotor_StopAll) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_SyncRotate) refers to stepmotor_api.o(i.StepMotor_RotateAngle) for StepMotor_RotateAngle
    stepmotor_api.o(i.StepMotor_SyncRotate) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_UpdateStepCount) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    stepmotor_api.o(i.StepMotor_UpdateStepCount) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    stepmotor_api.o(i.StepMotor_UpdateStepCount) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    stepmotor_api.o(i.StepMotor_UpdateStepCount) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    stepmotor_api.o(i.StepMotor_UpdateStepCount) refers to stepmotor_api.o(.data) for .data
    stepmotor_api.o(i.StepMotor_UpdateStepCount) refers to stepmotor_api.o(.bss) for .bss
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(.data) for .data
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.usart1_send) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to tim.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for .data
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    atd5984.o(i.ATD5984_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    atd5984.o(i.ATD5984_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    atd5984.o(i.ATD5984_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    atd5984.o(i.ATD5984_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    atd5984.o(i.STEP12_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    adc.o(i.ADC1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.ADC1_Init) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    adc.o(i.ADC1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adc.o(i.ADC1_Init) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    adc.o(i.Get_Adc1) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Get_Adc1) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    adc.o(i.Get_Adc1) refers to stm32f10x_adc.o(i.ADC_GetFlagStatus) for ADC_GetFlagStatus
    adc.o(i.Get_Adc1) refers to stm32f10x_adc.o(i.ADC_GetConversionValue) for ADC_GetConversionValue
    adc.o(i.Get_adc_Average) refers to adc.o(i.Get_Adc1) for Get_Adc1
    adc.o(i.Get_adc_Average) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tim.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    tim.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    tim.o(i.TIM2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tim.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tim.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    tim.o(i.TIM2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    tim.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing stepmotor_api.o(i.StepMotor_GetAngle), (52 bytes).
    Removing stepmotor_api.o(i.StepMotor_GetStatus), (48 bytes).
    Removing stepmotor_api.o(i.StepMotor_Reset), (56 bytes).
    Removing stepmotor_api.o(i.StepMotor_SetSpeed), (84 bytes).
    Removing stepmotor_api.o(i.StepMotor_SetTargetAngle), (116 bytes).
    Removing stepmotor_api.o(i.StepMotor_Stop), (76 bytes).
    Removing stepmotor_api.o(i.StepMotor_StopAll), (32 bytes).
    Removing stepmotor_api.o(i.StepMotor_SyncRotate), (44 bytes).
    Removing stepmotor_api.o(i.StepMotor_UpdateStepCount), (120 bytes).
    Removing stepmotor_api.o(.constdata), (240 bytes).
    Removing delay.o(i.delay_ms), (40 bytes).
    Removing delay.o(i.delay_us), (40 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.JTAG_Set), (40 bytes).
    Removing sys.o(i.MY_NVIC_SetVectorTable), (20 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing usart.o(i.fputc), (24 bytes).
    Removing usart.o(i.usart1_send), (20 bytes).
    Removing usart.o(.data), (4 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing misc.o(i.NVIC_Init), (100 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (24 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (68 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (72 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (116 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (18 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (16 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (36 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (20 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (204 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (16 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (232 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (140 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (142 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (90 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (190 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (108 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (28 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (24 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (28 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (20 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (28 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (24 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (24 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (22 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (228 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (32 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (58 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (32 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (112 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (56 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (124 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (60 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (40 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (80 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (136 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (96 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (34 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (42 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (18 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (104 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (200 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (98 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (26 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (102 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (180 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (40 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (28 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (88 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (44 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (36 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (14 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (188 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (6 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (20 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (20 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (16 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (64 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (44 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (40 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (150 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (22 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (84 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (14 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (44 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (56 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (108 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (368 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (300 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (128 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (108 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (70 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (46 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (116 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (28 bytes).
    Removing adc.o(i.ADC1_Init), (140 bytes).
    Removing adc.o(i.Get_Adc1), (52 bytes).
    Removing adc.o(i.Get_adc_Average), (46 bytes).
    Removing key.o(i.Key_Init), (44 bytes).
    Removing key.o(i.Key_Scan), (20 bytes).
    Removing tim.o(i.TIM2_Init), (90 bytes).

476 unused section(s) (total 15392 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HAREWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HAREWARE\ATD5984\ATD5984.c            0x00000000   Number         0  atd5984.o ABSOLUTE
    ..\HAREWARE\KEY\KEY.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HAREWARE\TIM\TIM.c                    0x00000000   Number         0  tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_bkp.c   0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_can.c   0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_cec.c   0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_crc.c   0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dac.c   0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dbgmcu.c 0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dma.c   0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_exti.c  0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_fsmc.c  0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_i2c.c   0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_iwdg.c  0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_pwr.c   0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rtc.c   0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_sdio.c  0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_spi.c   0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_tim.c   0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_wwdg.c  0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    StepMotor_API.c                          0x00000000   Number         0  stepmotor_api.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001aa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001ac   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001ac   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001ac   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001b2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001be   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001c0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001c0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001cc   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800020c   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800020e   Section        0  heapauxi.o(.text)
    .text                                    0x08000214   Section        2  use_no_semi.o(.text)
    .text                                    0x08000216   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000260   Section        0  exit.o(.text)
    .text                                    0x08000274   Section        8  libspace.o(.text)
    i.ATD5984_Init                           0x0800027c   Section        0  atd5984.o(i.ATD5984_Init)
    i.BusFault_Handler                       0x080002f0   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080002f2   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x080002f4   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ResetBits                         0x08000390   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000394   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x08000398   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x0800039a   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800039c   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_PriorityGroupConfig               0x080003a0   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x080003b4   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB2PeriphClockCmd                 0x080003b8   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x080003d0   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.STEP12_PWM_Init                        0x08000464   Section        0  atd5984.o(i.STEP12_PWM_Init)
    i.SVC_Handler                            0x0800050c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClockTo72                        0x08000510   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000511   Thumb Code   160  system_stm32f10x.o(i.SetSysClockTo72)
    i.StepMotor_Init                         0x080005b8   Section        0  stepmotor_api.o(i.StepMotor_Init)
    i.StepMotor_RotateAngle                  0x08000610   Section        0  stepmotor_api.o(i.StepMotor_RotateAngle)
    i.StepMotor_RotateSteps                  0x08000670   Section        0  stepmotor_api.o(i.StepMotor_RotateSteps)
    i.SysTick_Handler                        0x08000738   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800073c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x0800078c   Section        0  tim.o(i.TIM2_IRQHandler)
    i.TIM_ARRPreloadConfig                   0x080007a6   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_ClearITPendingBit                  0x080007ba   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x080007c0   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x080007d4   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_GetITStatus                        0x080007ea   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_OC3Init                            0x08000804   Section        0  stm32f10x_tim.o(i.TIM_OC3Init)
    i.TIM_OC3PreloadConfig                   0x08000880   Section        0  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    i.TIM_OC4Init                            0x0800088c   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PreloadConfig                   0x080008f0   Section        0  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_SetCompare3                        0x08000904   Section        0  stm32f10x_tim.o(i.TIM_SetCompare3)
    i.TIM_SetCompare4                        0x08000908   Section        0  stm32f10x_tim.o(i.TIM_SetCompare4)
    i.TIM_TimeBaseInit                       0x08000910   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x080009ac   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_ClearITPendingBit                0x080009d8   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x080009e4   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x080009f8   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08000a36   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08000a68   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08000b14   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x08000b1c   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i._CheckMotorID                          0x08000b1e   Section        0  stepmotor_api.o(i._CheckMotorID)
    _CheckMotorID                            0x08000b1f   Thumb Code    16  stepmotor_api.o(i._CheckMotorID)
    i._GetMotorIndex                         0x08000b2e   Section        0  stepmotor_api.o(i._GetMotorIndex)
    _GetMotorIndex                           0x08000b2f   Thumb Code    12  stepmotor_api.o(i._GetMotorIndex)
    i._sys_exit                              0x08000b3a   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x08000b3c   Section        0  delay.o(i.delay_init)
    i.main                                   0x08000b68   Section        0  main.o(i.main)
    i.uart_init                              0x08000b90   Section        0  usart.o(i.uart_init)
    x$fpl$fadd                               0x08000c18   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08000c27   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fdiv                               0x08000cdc   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08000cdd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffixu                              0x08000e60   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$ffltu                              0x08000ea0   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fmul                               0x08000ec8   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08000fca   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08001056   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frsb                               0x08001060   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsub                               0x08001074   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08001083   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$usenofp                            0x0800115e   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section        1  stepmotor_api.o(.data)
    g_system_initialized                     0x20000000   Data           1  stepmotor_api.o(.data)
    .data                                    0x20000002   Section        4  delay.o(.data)
    fac_us                                   0x20000002   Data           1  delay.o(.data)
    fac_ms                                   0x20000004   Data           2  delay.o(.data)
    .data                                    0x20000006   Section       20  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000006   Data           4  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000000a   Data          16  stm32f10x_rcc.o(.data)
    .bss                                     0x2000001c   Section       48  stepmotor_api.o(.bss)
    g_motors                                 0x2000001c   Data          48  stepmotor_api.o(.bss)
    .bss                                     0x2000004c   Section       96  libspace.o(.bss)
    HEAP                                     0x200000b0   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x200000b0   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x200002b0   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x200002b0   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x200006b0   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001ad   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001ad   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001ad   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001cd   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x080001e9   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __use_no_semihosting                     0x0800020d   Thumb Code     2  use_no_semi_2.o(.text)
    __use_two_region_memory                  0x0800020f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000211   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000213   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000215   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000215   Thumb Code     2  use_no_semi.o(.text)
    __user_setup_stackheap                   0x08000217   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000261   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000275   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000275   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000275   Thumb Code     0  libspace.o(.text)
    ATD5984_Init                             0x0800027d   Thumb Code   106  atd5984.o(i.ATD5984_Init)
    BusFault_Handler                         0x080002f1   Thumb Code     2  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080002f3   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x080002f5   Thumb Code   156  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ResetBits                           0x08000391   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000395   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x08000399   Thumb Code     2  stm32f10x_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x0800039b   Thumb Code     2  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800039d   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_PriorityGroupConfig                 0x080003a1   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x080003b5   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB2PeriphClockCmd                   0x080003b9   Thumb Code    18  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x080003d1   Thumb Code   132  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    STEP12_PWM_Init                          0x08000465   Thumb Code   160  atd5984.o(i.STEP12_PWM_Init)
    SVC_Handler                              0x0800050d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    StepMotor_Init                           0x080005b9   Thumb Code    78  stepmotor_api.o(i.StepMotor_Init)
    StepMotor_RotateAngle                    0x08000611   Thumb Code    86  stepmotor_api.o(i.StepMotor_RotateAngle)
    StepMotor_RotateSteps                    0x08000671   Thumb Code   178  stepmotor_api.o(i.StepMotor_RotateSteps)
    SysTick_Handler                          0x08000739   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x0800073d   Thumb Code    64  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x0800078d   Thumb Code    26  tim.o(i.TIM2_IRQHandler)
    TIM_ARRPreloadConfig                     0x080007a7   Thumb Code    20  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_ClearITPendingBit                    0x080007bb   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x080007c1   Thumb Code    20  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x080007d5   Thumb Code    22  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_GetITStatus                          0x080007eb   Thumb Code    24  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_OC3Init                              0x08000805   Thumb Code   116  stm32f10x_tim.o(i.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08000881   Thumb Code    12  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x0800088d   Thumb Code    90  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x080008f1   Thumb Code    20  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    TIM_SetCompare3                          0x08000905   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare3)
    TIM_SetCompare4                          0x08000909   Thumb Code     6  stm32f10x_tim.o(i.TIM_SetCompare4)
    TIM_TimeBaseInit                         0x08000911   Thumb Code   114  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x080009ad   Thumb Code    40  usart.o(i.USART1_IRQHandler)
    USART_ClearITPendingBit                  0x080009d9   Thumb Code    12  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x080009e5   Thumb Code    20  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x080009f9   Thumb Code    62  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08000a37   Thumb Code    48  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08000a69   Thumb Code   166  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08000b15   Thumb Code     8  stm32f10x_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x08000b1d   Thumb Code     2  stm32f10x_it.o(i.UsageFault_Handler)
    _sys_exit                                0x08000b3b   Thumb Code     2  usart.o(i._sys_exit)
    delay_init                               0x08000b3d   Thumb Code    38  delay.o(i.delay_init)
    main                                     0x08000b69   Thumb Code    36  main.o(i.main)
    uart_init                                0x08000b91   Thumb Code   126  usart.o(i.uart_init)
    __aeabi_fadd                             0x08000c19   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08000c19   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_fdiv                             0x08000cdd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08000cdd   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2uiz                            0x08000e61   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08000e61   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_ui2f                             0x08000ea1   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08000ea1   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_fmul                             0x08000ec9   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08000ec9   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08000fcb   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08001057   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_frsub                            0x08001061   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x08001061   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    __aeabi_fsub                             0x08001075   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08001075   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __I$use$fp                               0x0800115e   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08001160   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001180   Number         0  anon$$obj.o(Region$$Table)
    __libspace_start                         0x2000004c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000ac   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001cd

  Load Region LR_1 (Base: 0x08000000, Size: 0x0000119c, Max: 0xffffffff, ABSOLUTE)

    Execution Region ER_RO (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001180, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          477    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO         3518  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO         3702    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO         3704    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO         3706    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000002   Code   RO         3574    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3581    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3583    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3586    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3588    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3590    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3593    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3595    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3597    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3599    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3601    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3603    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3605    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3607    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3609    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3611    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3613    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3617    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3619    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3621    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         3623    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000002   Code   RO         3624    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a8   0x080001a8   0x00000002   Code   RO         3642    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         3652    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         3654    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         3656    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         3659    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         3662    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         3664    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         3667    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000002   Code   RO         3668    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO         3544    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO         3551    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001ac   0x080001ac   0x00000006   Code   RO         3563    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001b2   0x080001b2   0x00000000   Code   RO         3553    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000004   Code   RO         3554    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         3556    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000008   Code   RO         3557    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001be   0x080001be   0x00000002   Code   RO         3578    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         3626    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         3627    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c4   0x080001c4   0x00000006   Code   RO         3628    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ca   0x080001ca   0x00000002   PAD
    0x080001cc   0x080001cc   0x00000040   Code   RO          478  * .text               startup_stm32f10x_hd.o
    0x0800020c   0x0800020c   0x00000002   Code   RO         3514    .text               c_w.l(use_no_semi_2.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         3516    .text               c_w.l(heapauxi.o)
    0x08000214   0x08000214   0x00000002   Code   RO         3542    .text               c_w.l(use_no_semi.o)
    0x08000216   0x08000216   0x0000004a   Code   RO         3565    .text               c_w.l(sys_stackheap_outer.o)
    0x08000260   0x08000260   0x00000012   Code   RO         3567    .text               c_w.l(exit.o)
    0x08000272   0x08000272   0x00000002   PAD
    0x08000274   0x08000274   0x00000008   Code   RO         3575    .text               c_w.l(libspace.o)
    0x0800027c   0x0800027c   0x00000074   Code   RO         3431    i.ATD5984_Init      atd5984.o
    0x080002f0   0x080002f0   0x00000002   Code   RO          139    i.BusFault_Handler  stm32f10x_it.o
    0x080002f2   0x080002f2   0x00000002   Code   RO          140    i.DebugMon_Handler  stm32f10x_it.o
    0x080002f4   0x080002f4   0x0000009c   Code   RO         1631    i.GPIO_Init         stm32f10x_gpio.o
    0x08000390   0x08000390   0x00000004   Code   RO         1638    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000394   0x08000394   0x00000004   Code   RO         1639    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08000398   0x08000398   0x00000002   Code   RO          141    i.HardFault_Handler  stm32f10x_it.o
    0x0800039a   0x0800039a   0x00000002   Code   RO          142    i.MemManage_Handler  stm32f10x_it.o
    0x0800039c   0x0800039c   0x00000002   Code   RO          143    i.NMI_Handler       stm32f10x_it.o
    0x0800039e   0x0800039e   0x00000002   PAD
    0x080003a0   0x080003a0   0x00000014   Code   RO          483    i.NVIC_PriorityGroupConfig  misc.o
    0x080003b4   0x080003b4   0x00000002   Code   RO          144    i.PendSV_Handler    stm32f10x_it.o
    0x080003b6   0x080003b6   0x00000002   PAD
    0x080003b8   0x080003b8   0x00000018   Code   RO         2049    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080003d0   0x080003d0   0x00000094   Code   RO         2057    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08000464   0x08000464   0x000000a8   Code   RO         3432    i.STEP12_PWM_Init   atd5984.o
    0x0800050c   0x0800050c   0x00000002   Code   RO          145    i.SVC_Handler       stm32f10x_it.o
    0x0800050e   0x0800050e   0x00000002   PAD
    0x08000510   0x08000510   0x000000a8   Code   RO          228    i.SetSysClockTo72   system_stm32f10x.o
    0x080005b8   0x080005b8   0x00000058   Code   RO          259    i.StepMotor_Init    stepmotor_api.o
    0x08000610   0x08000610   0x00000060   Code   RO          261    i.StepMotor_RotateAngle  stepmotor_api.o
    0x08000670   0x08000670   0x000000c8   Code   RO          262    i.StepMotor_RotateSteps  stepmotor_api.o
    0x08000738   0x08000738   0x00000002   Code   RO          146    i.SysTick_Handler   stm32f10x_it.o
    0x0800073a   0x0800073a   0x00000002   PAD
    0x0800073c   0x0800073c   0x00000050   Code   RO          230    i.SystemInit        system_stm32f10x.o
    0x0800078c   0x0800078c   0x0000001a   Code   RO         3491    i.TIM2_IRQHandler   tim.o
    0x080007a6   0x080007a6   0x00000014   Code   RO         2666    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x080007ba   0x080007ba   0x00000006   Code   RO         2673    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x080007c0   0x080007c0   0x00000014   Code   RO         2678    i.TIM_Cmd           stm32f10x_tim.o
    0x080007d4   0x080007d4   0x00000016   Code   RO         2680    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x080007ea   0x080007ea   0x00000018   Code   RO         2699    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08000802   0x08000802   0x00000002   PAD
    0x08000804   0x08000804   0x0000007c   Code   RO         2717    i.TIM_OC3Init       stm32f10x_tim.o
    0x08000880   0x08000880   0x0000000c   Code   RO         2720    i.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x0800088c   0x0800088c   0x00000064   Code   RO         2722    i.TIM_OC4Init       stm32f10x_tim.o
    0x080008f0   0x080008f0   0x00000014   Code   RO         2724    i.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x08000904   0x08000904   0x00000004   Code   RO         2741    i.TIM_SetCompare3   stm32f10x_tim.o
    0x08000908   0x08000908   0x00000006   Code   RO         2742    i.TIM_SetCompare4   stm32f10x_tim.o
    0x0800090e   0x0800090e   0x00000002   PAD
    0x08000910   0x08000910   0x0000009c   Code   RO         2749    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x080009ac   0x080009ac   0x0000002c   Code   RO          416    i.USART1_IRQHandler  usart.o
    0x080009d8   0x080009d8   0x0000000c   Code   RO         3198    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x080009e4   0x080009e4   0x00000014   Code   RO         3201    i.USART_Cmd         stm32f10x_usart.o
    0x080009f8   0x080009f8   0x0000003e   Code   RO         3205    i.USART_GetITStatus  stm32f10x_usart.o
    0x08000a36   0x08000a36   0x00000030   Code   RO         3207    i.USART_ITConfig    stm32f10x_usart.o
    0x08000a66   0x08000a66   0x00000002   PAD
    0x08000a68   0x08000a68   0x000000ac   Code   RO         3208    i.USART_Init        stm32f10x_usart.o
    0x08000b14   0x08000b14   0x00000008   Code   RO         3215    i.USART_ReceiveData  stm32f10x_usart.o
    0x08000b1c   0x08000b1c   0x00000002   Code   RO          147    i.UsageFault_Handler  stm32f10x_it.o
    0x08000b1e   0x08000b1e   0x00000010   Code   RO          269    i._CheckMotorID     stepmotor_api.o
    0x08000b2e   0x08000b2e   0x0000000c   Code   RO          270    i._GetMotorIndex    stepmotor_api.o
    0x08000b3a   0x08000b3a   0x00000002   Code   RO          417    i._sys_exit         usart.o
    0x08000b3c   0x08000b3c   0x0000002c   Code   RO          349    i.delay_init        delay.o
    0x08000b68   0x08000b68   0x00000028   Code   RO            1    i.main              main.o
    0x08000b90   0x08000b90   0x00000088   Code   RO          419    i.uart_init         usart.o
    0x08000c18   0x08000c18   0x000000c4   Code   RO         3520    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08000cdc   0x08000cdc   0x00000184   Code   RO         3527    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08000e60   0x08000e60   0x0000003e   Code   RO         3530    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08000e9e   0x08000e9e   0x00000002   PAD
    0x08000ea0   0x08000ea0   0x00000026   Code   RO         3534    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x08000ec6   0x08000ec6   0x00000002   PAD
    0x08000ec8   0x08000ec8   0x00000102   Code   RO         3540    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08000fca   0x08000fca   0x0000008c   Code   RO         3545    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08001056   0x08001056   0x0000000a   Code   RO         3547    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08001060   0x08001060   0x00000014   Code   RO         3521    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x08001074   0x08001074   0x000000ea   Code   RO         3522    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x0800115e   0x0800115e   0x00000000   Code   RO         3549    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x0800115e   0x0800115e   0x00000002   PAD
    0x08001160   0x08001160   0x00000020   Data   RO         3700    Region$$Table       anon$$obj.o


    Execution Region ER_RW (Exec base: 0x20000000, Load base: 0x08001180, Size: 0x0000001c, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08001180   0x00000001   Data   RW          273    .data               stepmotor_api.o
    0x20000001   0x08001181   0x00000001   PAD
    0x20000002   0x08001182   0x00000004   Data   RW          352    .data               delay.o
    0x20000006   0x08001186   0x00000014   Data   RW         2077    .data               stm32f10x_rcc.o


    Execution Region ER_ZI (Exec base: 0x2000001c, Load base: 0x0800119c, Size: 0x00000694, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x2000001c        -       0x00000030   Zero   RW          271    .bss                stepmotor_api.o
    0x2000004c        -       0x00000060   Zero   RW         3576    .bss                c_w.l(libspace.o)
    0x200000ac   0x0800119c   0x00000004   PAD
    0x200000b0        -       0x00000200   Zero   RW          476    HEAP                startup_stm32f10x_hd.o
    0x200002b0        -       0x00000400   Zero   RW          475    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       284         18          0          0          0       1405   atd5984.o
        44          6          0          4          0        773   delay.o
        40          4          0          0          0     252711   main.o
        20         10          0          0          0        551   misc.o
        64         26        304          0       1536        824   startup_stm32f10x_hd.o
       412         42          0          1         48       4185   stepmotor_api.o
       164          0          0          0          0       3244   stm32f10x_gpio.o
        18          0          0          0          0       4038   stm32f10x_it.o
       172         22          0         20          0       4085   stm32f10x_rcc.o
       514         60          0          0          0       9416   stm32f10x_tim.o
       322          6          0          0          0       6052   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       248         24          0          0          0       1561   system_stm32f10x.o
        26          0          0          0          0        530   tim.o
       182         14          0          0          0       1788   usart.o

    ----------------------------------------------------------------------
      2524        <USER>        <GROUP>         28       1584     291195   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          0          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
       450          8          0          0          0        236   faddsub_clz.o
       388         76          0          0          0         96   fdiv.o
        62          4          0          0          0         84   ffixu.o
        38          0          0          0          0         68   fflt_clz.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      1620        <USER>          <GROUP>          0        100       1304   Library Totals
        12          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       262         12          0          0         96        584   c_w.l
      1346         96          0          0          0        720   fz_ws.l

    ----------------------------------------------------------------------
      1620        <USER>          <GROUP>          0        100       1304   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      4144        340        336         28       1684     289635   Grand Totals
      4144        340        336         28       1684     289635   ELF Image Totals
      4144        340        336         28          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 4480 (   4.38kB)
    Total RW  Size (RW Data + ZI Data)              1712 (   1.67kB)
    Total ROM Size (Code + RO Data + RW Data)       4508 (   4.40kB)

==============================================================================

